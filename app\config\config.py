from apscheduler.schedulers.asyncio import AsyncIOScheduler
from app.service.key.key_manager import get_key_manager_instance
from app.service.chat.gemini_chat_service import GeminiChatService
from app.service.log_cleanup_service import get_log_cleanup_service
from app.domain.gemini_models import GeminiRequest, GeminiContent
from app.config.config import settings
from app.log.logger import Logger # 导入 Logger 类

logger = Logger.setup_logger("scheduler") # 使用 Logger.setup_logger

async def check_failed_keys():
    """
    定时检查失败次数大于 0 的 API 密钥，并尝试验证它们。
    如果验证成功，重置失败计数；如果失败，增加失败计数。
    """
    logger.info("Starting scheduled check for failed API keys...")
    try:
        key_manager = await get_key_manager_instance()
        # 确保 KeyManager 已经初始化
        if not key_manager or not hasattr(key_manager, 'key_failure_counts'):
            logger.warning("KeyManager instance not available or not initialized. Skipping check.")
            return

        # 创建 GeminiChatService 实例用于验证
        # 注意：这里直接创建实例，而不是通过依赖注入，因为这是后台任务
        chat_service = GeminiChatService(settings.BASE_URL, key_manager)

        # 获取需要检查的 key 列表 (失败次数 > 0)
        keys_to_check = []
        async with key_manager.failure_count_lock: # 访问共享数据需要加锁
            # 复制一份以避免在迭代时修改字典
            failure_counts_copy = key_manager.key_failure_counts.copy()
            keys_to_check = [key for key, count in failure_counts_copy.items() if count > 0] # 检查所有失败次数大于 0 的 key

        if not keys_to_check:
            logger.info("No keys with failure count > 0 found. Skipping verification.")
            return

        logger.info(f"Found {len(keys_to_check)} keys with failure count > 0 to verify.")

        for key in keys_to_check:
            # 隐藏部分 key 用于日志记录
            log_key = f"{key[:4]}...{key[-4:]}" if len(key) > 8 else key
            logger.info(f"Verifying key: {log_key}...")
            try:
                # 构造测试请求
                gemini_request = GeminiRequest(
                    contents=[
                        GeminiContent(
                            role="user",
                            parts=[{"text": "hi"}] # 使用简单的文本进行验证
                        )
                    ]
                )
                # 调用 generate_content 进行验证
                await chat_service.generate_content(
                    settings.TEST_MODEL, # 使用配置中定义的测试模型
                    gemini_request,
                    key
                )
                # 如果没有抛出异常，说明 key 有效
                logger.info(f"Key {log_key} verification successful. Resetting failure count.")
                await key_manager.reset_key_failure_count(key)
            except Exception as e:
                # 验证失败，增加失败计数
                logger.warning(f"Key {log_key} verification failed: {str(e)}. Incrementing failure count.")
                # 直接操作计数器，需要加锁
                async with key_manager.failure_count_lock:
                    # 再次检查 key 是否存在且失败次数未达上限
                    if key in key_manager.key_failure_counts and key_manager.key_failure_counts[key] < key_manager.MAX_FAILURES:
                        key_manager.key_failure_counts[key] += 1
                        logger.info(f"Failure count for key {log_key} incremented to {key_manager.key_failure_counts[key]}.")
                    elif key in key_manager.key_failure_counts:
                         logger.warning(f"Key {log_key} reached MAX_FAILURES ({key_manager.MAX_FAILURES}). Not incrementing further.")


    except Exception as e:
        logger.error(f"An error occurred during the scheduled key check: {str(e)}", exc_info=True)

async def cleanup_logs():
    """
    定时清理过期的日志记录
    """
    logger.info("Starting scheduled log cleanup...")
    try:
        if not settings.LOG_CLEANUP_ENABLED:
            logger.info("Log cleanup is disabled. Skipping cleanup.")
            return

        log_cleanup_service = get_log_cleanup_service()
        result = await log_cleanup_service.cleanup_old_logs()

        if result["status"] == "success":
            logger.info(f"Log cleanup completed successfully. "
                       f"Request logs cleaned: {result['request_logs_cleaned']}, "
                       f"Error logs cleaned: {result['error_logs_cleaned']}, "
                       f"Duration: {result['duration_seconds']:.2f}s")
        elif result["status"] == "skipped":
            logger.info(f"Log cleanup skipped: {result['reason']}")
        else:
            logger.error(f"Log cleanup failed: {result.get('error', 'Unknown error')}")

    except Exception as e:
        logger.error(f"An error occurred during the scheduled log cleanup: {str(e)}", exc_info=True)

def setup_scheduler():
    """设置并启动 APScheduler"""
    scheduler = AsyncIOScheduler(timezone=str(settings.TIMEZONE)) # 从配置读取时区

    # 添加 API 密钥检查任务
    scheduler.add_job(check_failed_keys, 'interval', hours=settings.CHECK_INTERVAL_HOURS)
    logger.info(f"Key check job scheduled to run every {settings.CHECK_INTERVAL_HOURS} hour(s).")

    # 添加日志清理任务
    if settings.LOG_CLEANUP_ENABLED:
        scheduler.add_job(cleanup_logs, 'interval', hours=settings.LOG_CLEANUP_INTERVAL_HOURS)
        logger.info(f"Log cleanup job scheduled to run every {settings.LOG_CLEANUP_INTERVAL_HOURS} hour(s).")
    else:
        logger.info("Log cleanup is disabled.")

    scheduler.start()
    logger.info("Scheduler started successfully.")
    return scheduler

# 可以在这里添加一个全局的 scheduler 实例，以便在应用关闭时优雅地停止
scheduler_instance = None

def start_scheduler():
    global scheduler_instance
    if scheduler_instance is None or not scheduler_instance.running:
        logger.info("Starting scheduler...")
        scheduler_instance = setup_scheduler()
    logger.info("Scheduler is already running.")

def stop_scheduler():
    global scheduler_instance
    if scheduler_instance and scheduler_instance.running:
        scheduler_instance.shutdown()
        logger.info("Scheduler stopped."), str]]:
            try:
                parsed = json.loads(db_value)
                if isinstance(parsed, list):
                    # 验证列表中的每个元素是否为字典，并且键和值都是字符串
                    valid = all(
                        isinstance(item, dict) and
                        all(isinstance(k, str) for k in item.keys()) and
                        all(isinstance(v, str) for v in item.values())
                        for item in parsed
                    )
                    if valid:
                        return parsed
                    else:
                        logger.warning(f"Invalid structure in List[Dict[str, str]] for key '{key}'. Value: {db_value}")
                        return [] # 或者返回默认值？这里返回空列表
                else:
                     logger.warning(f"Parsed DB value for key '{key}' is not a list type. Value: {db_value}")
                     return []
            except json.JSONDecodeError:
                logger.error(f"Could not parse '{db_value}' as JSON for List[Dict[str, str]] for key '{key}'. Returning empty list.")
                return []
            except Exception as e:
                logger.error(f"Error parsing List[Dict[str, str]] for key '{key}': {e}. Value: {db_value}. Returning empty list.")
                return []
        # 处理 bool
        elif target_type == bool:
            return db_value.lower() in ('true', '1', 'yes', 'on')
        # 处理 int
        elif target_type == int:
            return int(db_value)
        # 处理 float
        elif target_type == float:
            return float(db_value)
        # 默认为 str 或其他 pydantic 能直接处理的类型
        else:
            return db_value
    except (ValueError, TypeError, json.JSONDecodeError) as e:
        logger.warning(f"Failed to parse db_value '{db_value}' for key '{key}' as type {target_type}: {e}. Using original string value.")
        return db_value # 解析失败则返回原始字符串

async def sync_initial_settings():
    """
    应用启动时同步配置：
    1. 从数据库加载设置。
    2. 将数据库设置合并到内存 settings (数据库优先)。
    3. 将最终的内存 settings 同步回数据库。
    """
    from app.log.logger import get_config_logger # 函数内导入
    logger = get_config_logger() # 函数内初始化
    # 延迟导入以避免循环依赖和确保数据库连接已初始化
    from app.database.connection import database
    from app.database.models import Settings as SettingsModel

    global settings
    logger.info("Starting initial settings synchronization...")

    if not database.is_connected:
        try:
            await database.connect()
            logger.info("Database connection established for initial sync.")
        except Exception as e:
            logger.error(f"Failed to connect to database for initial settings sync: {e}. Skipping sync.")
            return

    try:
        # 1. 从数据库加载设置
        db_settings_raw: List[Dict[str, Any]] = []
        try:
            query = select(SettingsModel.key, SettingsModel.value)
            results = await database.fetch_all(query)
            db_settings_raw = [{"key": row["key"], "value": row["value"]} for row in results]
            logger.info(f"Fetched {len(db_settings_raw)} settings from database.")
        except Exception as e:
            logger.error(f"Failed to fetch settings from database: {e}. Proceeding with environment/dotenv settings.")
            # 即使数据库读取失败，也要继续执行，确保基于 env/dotenv 的配置能同步到数据库

        db_settings_map: Dict[str, str] = {s['key']: s['value'] for s in db_settings_raw}

        # 2. 将数据库设置合并到内存 settings (数据库优先)
        updated_in_memory = False

        for key, db_value in db_settings_map.items():
            if hasattr(settings, key):
                target_type = Settings.__annotations__.get(key)
                if target_type:
                    try:
                        parsed_db_value = _parse_db_value(key, db_value, target_type)
                        memory_value = getattr(settings, key)

                        # 比较解析后的值和内存中的值
                        # 注意：对于列表等复杂类型，直接比较可能不够健壮，但这里简化处理
                        if parsed_db_value != memory_value:
                            # 检查类型是否匹配，以防解析函数返回了不兼容的类型
                            type_match = False
                            if target_type == List[str] and isinstance(parsed_db_value, list):
                                type_match = True
                            elif target_type == Dict[str, float] and isinstance(parsed_db_value, dict):
                                type_match = True
                            elif target_type not in (List[str], Dict[str, float]) and isinstance(parsed_db_value, target_type):
                                type_match = True

                            if type_match:
                                setattr(settings, key, parsed_db_value)
                                logger.debug(f"Updated setting '{key}' in memory from database value ({target_type}).")
                                updated_in_memory = True
                            else:
                                logger.warning(f"Parsed DB value type mismatch for key '{key}'. Expected {target_type}, got {type(parsed_db_value)}. Skipping update.")

                    except Exception as e:
                        logger.error(f"Error processing database setting for key '{key}': {e}")
            else:
                 logger.warning(f"Database setting '{key}' not found in Settings model definition. Ignoring.")


        # 如果内存中有更新，重新验证 Pydantic 模型（可选但推荐）
        if updated_in_memory:
            try:
                # 重新加载以确保类型转换和验证
                settings = Settings(**settings.model_dump())
                logger.info("Settings object re-validated after merging database values.")
            except ValidationError as e:
                 logger.error(f"Validation error after merging database settings: {e}. Settings might be inconsistent.")


        # 3. 将最终的内存 settings 同步回数据库
        final_memory_settings = settings.model_dump()
        settings_to_update: List[Dict[str, Any]] = []
        settings_to_insert: List[Dict[str, Any]] = []
        now = datetime.datetime.now(datetime.timezone.utc)

        existing_db_keys = set(db_settings_map.keys())

        for key, value in final_memory_settings.items():
            # 序列化值为字符串或 JSON 字符串
            if isinstance(value, (list, dict)): # 处理列表和字典
                db_value = json.dumps(value, ensure_ascii=False) # 使用 ensure_ascii=False 以支持非 ASCII 字符
            elif isinstance(value, bool):
                db_value = str(value).lower()
            elif value is None: # 处理 None 值
                db_value = "" # 或者根据需要设为 NULL 或其他标记
            else:
                db_value = str(value)

            data = {
                'key': key,
                'value': db_value,
                'description': f"{key} configuration setting", # 默认描述
                'updated_at': now
            }

            if key in existing_db_keys:
                # 仅当值与数据库中的不同时才更新
                if db_settings_map[key] != db_value:
                    settings_to_update.append(data)
            else:
                # 如果键不在数据库中，则插入
                data['created_at'] = now
                settings_to_insert.append(data)

        # 在事务中执行批量插入和更新
        if settings_to_insert or settings_to_update:
            try:
                async with database.transaction():
                    if settings_to_insert:
                        # 获取现有描述以避免覆盖
                        query_existing = select(SettingsModel.key, SettingsModel.description).where(SettingsModel.key.in_([s['key'] for s in settings_to_insert]))
                        existing_desc = {row['key']: row['description'] for row in await database.fetch_all(query_existing)}
                        for item in settings_to_insert:
                            item['description'] = existing_desc.get(item['key'], item['description'])

                        query_insert = insert(SettingsModel).values(settings_to_insert)
                        await database.execute(query=query_insert)
                        logger.info(f"Synced (inserted) {len(settings_to_insert)} settings to database.")

                    if settings_to_update:
                        # 获取现有描述以避免覆盖
                        query_existing = select(SettingsModel.key, SettingsModel.description).where(SettingsModel.key.in_([s['key'] for s in settings_to_update]))
                        existing_desc = {row['key']: row['description'] for row in await database.fetch_all(query_existing)}

                        for setting_data in settings_to_update:
                            setting_data['description'] = existing_desc.get(setting_data['key'], setting_data['description'])
                            query_update = (
                                update(SettingsModel)
                                .where(SettingsModel.key == setting_data['key'])
                                .values(
                                    value=setting_data['value'],
                                    description=setting_data['description'],
                                    updated_at=setting_data['updated_at']
                                )
                            )
                            await database.execute(query=query_update)
                        logger.info(f"Synced (updated) {len(settings_to_update)} settings to database.")
            except Exception as e:
                logger.error(f"Failed to sync settings to database during startup: {str(e)}")
        else:
            logger.info("No setting changes detected between memory and database during initial sync.")

        # 刷新日志等级
        log_level = final_memory_settings.get("LOG_LEVEL", "INFO")
        if isinstance(log_level, str):
            Logger.update_log_levels(log_level)

    except Exception as e:
        logger.error(f"An unexpected error occurred during initial settings sync: {e}")
    finally:
        if database.is_connected:
             try:
                 pass
             except Exception as e:
                 logger.error(f"Error disconnecting database after initial sync: {e}")

    logger.info("Initial settings synchronization finished.")
