"""
日志清理服务模块
"""
import asyncio
from datetime import datetime, timedelta
from typing import Dict, Any, Optional

from sqlalchemy import select, delete, func

from app.config.config import settings
from app.database.connection import database
from app.database.models import RequestLog, ErrorLog
from app.log.logger import Logger

logger = Logger.setup_logger("log_cleanup")


class LogCleanupService:
    """日志清理服务"""
    
    def __init__(self):
        self.is_running = False
        self.cleanup_stats = {
            "last_cleanup_time": None,
            "total_request_logs_cleaned": 0,
            "total_error_logs_cleaned": 0,
            "last_cleanup_duration": 0
        }
    
    async def cleanup_old_logs(self) -> Dict[str, Any]:
        """
        清理过期的日志记录
        
        Returns:
            Dict[str, Any]: 清理统计信息
        """
        if self.is_running:
            logger.warning("Log cleanup is already running, skipping this execution")
            return {"status": "skipped", "reason": "already_running"}
        
        self.is_running = True
        start_time = datetime.now()
        
        try:
            logger.info("Starting log cleanup process...")
            
            # 计算清理的截止时间
            request_log_cutoff = datetime.now() - timedelta(days=settings.REQUEST_LOG_RETENTION_DAYS)
            error_log_cutoff = datetime.now() - timedelta(days=settings.ERROR_LOG_RETENTION_DAYS)
            
            # 清理请求日志
            request_logs_cleaned = await self._cleanup_request_logs(request_log_cutoff)
            
            # 清理错误日志
            error_logs_cleaned = await self._cleanup_error_logs(error_log_cutoff)
            
            # 更新统计信息
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            self.cleanup_stats.update({
                "last_cleanup_time": end_time,
                "total_request_logs_cleaned": self.cleanup_stats["total_request_logs_cleaned"] + request_logs_cleaned,
                "total_error_logs_cleaned": self.cleanup_stats["total_error_logs_cleaned"] + error_logs_cleaned,
                "last_cleanup_duration": duration
            })
            
            logger.info(f"Log cleanup completed. Request logs cleaned: {request_logs_cleaned}, "
                       f"Error logs cleaned: {error_logs_cleaned}, Duration: {duration:.2f}s")
            
            return {
                "status": "success",
                "request_logs_cleaned": request_logs_cleaned,
                "error_logs_cleaned": error_logs_cleaned,
                "duration_seconds": duration,
                "cleanup_time": end_time.isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error during log cleanup: {str(e)}", exc_info=True)
            return {
                "status": "error",
                "error": str(e),
                "cleanup_time": datetime.now().isoformat()
            }
        finally:
            self.is_running = False
    
    async def _cleanup_request_logs(self, cutoff_time: datetime) -> int:
        """
        清理过期的请求日志
        
        Args:
            cutoff_time: 清理截止时间
            
        Returns:
            int: 清理的记录数量
        """
        try:
            # 先统计要删除的记录数
            count_query = select(func.count(RequestLog.id)).where(
                RequestLog.request_time < cutoff_time
            )
            total_count = await database.fetch_val(count_query)
            
            if total_count == 0:
                logger.info("No request logs to cleanup")
                return 0
            
            logger.info(f"Found {total_count} request logs to cleanup (older than {cutoff_time})")
            
            # 分批删除以避免长时间锁表
            cleaned_count = 0
            batch_size = settings.LOG_CLEANUP_BATCH_SIZE
            
            while cleaned_count < total_count:
                # 获取一批要删除的ID
                id_query = select(RequestLog.id).where(
                    RequestLog.request_time < cutoff_time
                ).limit(batch_size)
                
                ids_to_delete = await database.fetch_all(id_query)
                if not ids_to_delete:
                    break
                
                # 删除这批记录
                delete_query = delete(RequestLog).where(
                    RequestLog.id.in_([row["id"] for row in ids_to_delete])
                )
                await database.execute(delete_query)
                
                batch_cleaned = len(ids_to_delete)
                cleaned_count += batch_cleaned
                
                logger.debug(f"Cleaned {batch_cleaned} request logs, total: {cleaned_count}/{total_count}")
                
                # 短暂休息以减少数据库压力
                if cleaned_count < total_count:
                    await asyncio.sleep(0.1)
            
            logger.info(f"Successfully cleaned {cleaned_count} request logs")
            return cleaned_count
            
        except Exception as e:
            logger.error(f"Error cleaning request logs: {str(e)}", exc_info=True)
            raise
    
    async def _cleanup_error_logs(self, cutoff_time: datetime) -> int:
        """
        清理过期的错误日志
        
        Args:
            cutoff_time: 清理截止时间
            
        Returns:
            int: 清理的记录数量
        """
        try:
            # 先统计要删除的记录数
            count_query = select(func.count(ErrorLog.id)).where(
                ErrorLog.request_time < cutoff_time
            )
            total_count = await database.fetch_val(count_query)
            
            if total_count == 0:
                logger.info("No error logs to cleanup")
                return 0
            
            logger.info(f"Found {total_count} error logs to cleanup (older than {cutoff_time})")
            
            # 分批删除以避免长时间锁表
            cleaned_count = 0
            batch_size = settings.LOG_CLEANUP_BATCH_SIZE
            
            while cleaned_count < total_count:
                # 获取一批要删除的ID
                id_query = select(ErrorLog.id).where(
                    ErrorLog.request_time < cutoff_time
                ).limit(batch_size)
                
                ids_to_delete = await database.fetch_all(id_query)
                if not ids_to_delete:
                    break
                
                # 删除这批记录
                delete_query = delete(ErrorLog).where(
                    ErrorLog.id.in_([row["id"] for row in ids_to_delete])
                )
                await database.execute(delete_query)
                
                batch_cleaned = len(ids_to_delete)
                cleaned_count += batch_cleaned
                
                logger.debug(f"Cleaned {batch_cleaned} error logs, total: {cleaned_count}/{total_count}")
                
                # 短暂休息以减少数据库压力
                if cleaned_count < total_count:
                    await asyncio.sleep(0.1)
            
            logger.info(f"Successfully cleaned {cleaned_count} error logs")
            return cleaned_count
            
        except Exception as e:
            logger.error(f"Error cleaning error logs: {str(e)}", exc_info=True)
            raise
    
    async def get_log_statistics(self) -> Dict[str, Any]:
        """
        获取日志统计信息
        
        Returns:
            Dict[str, Any]: 日志统计信息
        """
        try:
            # 获取请求日志统计
            request_count_query = select(func.count(RequestLog.id))
            request_count = await database.fetch_val(request_count_query)
            
            # 获取错误日志统计
            error_count_query = select(func.count(ErrorLog.id))
            error_count = await database.fetch_val(error_count_query)
            
            # 获取最早的日志时间
            earliest_request_query = select(func.min(RequestLog.request_time))
            earliest_request = await database.fetch_val(earliest_request_query)
            
            earliest_error_query = select(func.min(ErrorLog.request_time))
            earliest_error = await database.fetch_val(earliest_error_query)
            
            return {
                "request_logs": {
                    "total_count": request_count or 0,
                    "earliest_log": earliest_request.isoformat() if earliest_request else None
                },
                "error_logs": {
                    "total_count": error_count or 0,
                    "earliest_log": earliest_error.isoformat() if earliest_error else None
                },
                "cleanup_stats": self.cleanup_stats,
                "retention_settings": {
                    "request_log_retention_days": settings.REQUEST_LOG_RETENTION_DAYS,
                    "error_log_retention_days": settings.ERROR_LOG_RETENTION_DAYS,
                    "cleanup_enabled": settings.LOG_CLEANUP_ENABLED,
                    "cleanup_interval_hours": settings.LOG_CLEANUP_INTERVAL_HOURS
                }
            }
            
        except Exception as e:
            logger.error(f"Error getting log statistics: {str(e)}", exc_info=True)
            raise


# 全局日志清理服务实例
_log_cleanup_service: Optional[LogCleanupService] = None


def get_log_cleanup_service() -> LogCleanupService:
    """获取日志清理服务实例"""
    global _log_cleanup_service
    if _log_cleanup_service is None:
        _log_cleanup_service = LogCleanupService()
    return _log_cleanup_service
